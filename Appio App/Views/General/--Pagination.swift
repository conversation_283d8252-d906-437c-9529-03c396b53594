//
//  --Pagination.swift
//  Appio
//
//  Created by gondo on 10/03/2025.
//

import SwiftUI

struct Home: View {
    var colors: [Color] = [.red, .blue, .pink, .purple]

    @State private var selection: Int = 0

    private let spacing: CGFloat = 15
    private let capsuleSize: CGFloat = 7
    private let capsuleJoiner: CGFloat = 20

    var body: some View {
        GeometryReader { geometry in
            TabView(selection: $selection) {
                ForEach(colors.indices, id: \.self) { index in
                    colors[index]
                        .ignoresSafeArea()
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .overlay(alignment: .bottom) {
                HStack(spacing: spacing) {
                    ForEach(colors.indices, id: \.self) { index in
                        Capsule()
                            .fill(.white)
                            .frame(width: selection == index ? capsuleJoiner : capsuleSize, height: capsuleSize)
                    }
                }
                .overlay(alignment: .leading) {
                    Capsule()
                        .fill(.white)
                        .frame(width: capsuleJoiner, height: capsuleSize)
                        .offset(x: getOffset())
                        .animation(.easeInOut, value: selection)
                }
                .padding(.bottom, geometry.safeAreaInsets.bottom)
            }
            .ignoresSafeArea(.all)
        }
    }

    func getOffset() -> CGFloat {
        return CGFloat(selection) * (capsuleSize + spacing)
    }
}

#Preview {
    Home()
}
