//
//  ArrowAnimation.swift
//  Appio
//
//  Created by gondo on 14/04/2025.
//

import SwiftUI

struct ArrowAnimation: View {
    @State private var arrowAnimationProgress = 0.0
    @State private var isAnimatingArrow = false

    private let arrowAnimationDuration: TimeInterval = 0.5
    private let arrowAnimationPause: TimeInterval = 0.5

    var body: some View {
        GeometryReader { geometry in
            Image("ArrowIndicator")
                .resizable()
                .scaledToFit()
                .scaleEffect(0.5 + (0.5 * arrowAnimationProgress), anchor: .bottom)
                .offset(y: geometry.size.height / 2 - (geometry.size.height / 2 * arrowAnimationProgress))
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                .opacity(0.0 + 0.5 * arrowAnimationProgress)
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                startArrowAnimation()
            }
        }
        .onDisappear {
            isAnimatingArrow = false
        }
    }

    private func startArrowAnimation() {
        isAnimatingArrow = true
        arrowAnimationProgress = 0
        arrowAnimateCycle()
    }

    private func arrowAnimateCycle() {
        guard isAnimatingArrow else { return }

        arrowAnimationProgress = 0

        withAnimation(.easeOut(duration: arrowAnimationDuration)) {
            arrowAnimationProgress = 1
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + arrowAnimationDuration + arrowAnimationPause) {
            if isAnimatingArrow {
                arrowAnimateCycle()
            }
        }
    }
}

#Preview {
    ArrowAnimation()
}
