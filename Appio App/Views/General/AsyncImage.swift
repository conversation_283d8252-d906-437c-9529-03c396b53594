//
//  AsyncImage.swift
//  Appio App
//
//  Created by gondo on 09/07/2023.
//

import SwiftUI

struct AsyncImageView: View {
    let url: URL?
    let contentMode: ContentMode
    let cornerRadius: CGFloat

    init(
        urlString: String?,
        contentMode: ContentMode = .fill,
        cornerRadius: CGFloat = 0
    ) {
        url = URL(string: urlString ?? "")
        self.contentMode = contentMode
        self.cornerRadius = cornerRadius
    }

    var body: some View {
        GeometryReader { _ in
            if let url = url {
                CachedAsyncImage(url: url) { phase in
                    switch phase {
                    case let .success(image):
                        if cornerRadius > 0 {
                            image.resizable()
                                .aspectRatio(contentMode: contentMode)
                                .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                        } else {
                            image.resizable()
                                .aspectRatio(contentMode: contentMode)
                        }
                    case .failure:
                        ImagePlaceholderView(cornerRadius: cornerRadius)
                    case .empty:
                        LoadingViewView(cornerRadius: cornerRadius)
                    @unknown default:
                        ImagePlaceholderView(cornerRadius: cornerRadius)
                    }
                }
            } else {
                ImagePlaceholderView(cornerRadius: cornerRadius)
            }
        }
    }
}

// TODO: not using cached image: Image.cached(url: url) or should we just pass local cached URL?
private struct CachedAsyncImage<Content: View>: View {
    let url: URL
    @ViewBuilder let content: (AsyncImagePhase) -> Content

    var body: some View {
        ZStack {
            Color.clear // fill the space

            AsyncImage(url: url) { phase in
                content(phase)
            }
        }
        .task {
            print("Debug: \(url.absoluteString)")
            await ImageManager.shared.preCacheImage(url.absoluteString)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Banner style
        AsyncImageView(urlString: "https://picsum.photos/1600/900")
            .aspectRatio(16 / 9, contentMode: .fit)
            .frame(width: 300)

        // Logo style
        AsyncImageView(urlString: "https://picsum.photos/200", contentMode: .fit, cornerRadius: 15)
            .frame(width: 48, height: 48)

        // Failed loading
        AsyncImageView(urlString: "invalid-url", cornerRadius: 10)
            .frame(width: 100, height: 100)
    }
    .padding()
}
