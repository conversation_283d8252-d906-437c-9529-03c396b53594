//
//  LoadingView 2.swift
//  Appio
//
//  Created by gondo on 07/03/2025.
//

// loader

import SwiftUI

struct LoadingView2: View {
    @State private var trimFrom: CGFloat = 0.0
    @State private var trimTo: CGFloat = 0.0
    @State private var phase = 0

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ZStack {
                    Circle()
                        .trim(from: trimFrom, to: trimTo)
                        .stroke(style: StrokeStyle(lineWidth: 1, lineCap: .round))
                        .frame(width: min(geometry.size.width, geometry.size.height) * 0.25, height: min(geometry.size.width, geometry.size.height) * 0.25)
                        .foregroundStyle(Color.primary.opacity(0.75))
                        .rotationEffect(.degrees(-90))
                    Circle()
                        .stroke(style: StrokeStyle(lineWidth: 1, lineCap: .round))
                        .frame(width: min(geometry.size.width, geometry.size.height) * 0.25, height: min(geometry.size.width, geometry.size.height) * 0.25)
                        .foregroundStyle(Color.secondary.opacity(0.25))
                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .onAppear {
            startPhaseAnimation()
        }
    }

    private func startPhaseAnimation() {
        let duration = 1.3

        switch phase {
        case 0:
            withAnimation(.easeInOut(duration: duration)) {
                trimTo = 1.0
            }

        case 1:
            withAnimation(.easeInOut(duration: duration)) {
                trimFrom = 1.0
            }

        case 2:
            trimFrom = 0.0
            trimTo = 0.0

        default: // never triggered
            phase = 0
            trimFrom = 0.0
            trimTo = 0.0
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + (phase == 2 ? 0 : duration)) {
            phase = (phase + 1) % 3
            startPhaseAnimation()
        }
    }
}

#Preview {
    LoadingView2()
}
