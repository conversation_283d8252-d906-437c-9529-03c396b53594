import SwiftUI

struct LoadingView: View {
    private let dashCount = 120

    @State private var currentIndex = 0
    @State private var timer: Timer? = nil
    @State private var appearOpacity: Double = 0
    @State private var appoarScale: Double = 0.9

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ZStack {
                    let circleSize = min(geometry.size.width, geometry.size.height) * 0.3
                    let offsetRadius = circleSize / 2
                    let dashWidth = circleSize / 115
                    let dashHeight = circleSize / 18

                    ForEach(0 ..< dashCount, id: \.self) { index in
                        Capsule()
                            .fill(Color.primary.opacity(self.opacityFor(index: index)))
                            .frame(width: dashWidth, height: dashHeight)
                            .offset(y: -offsetRadius) // Position on circle edge
                            .rotationEffect(.degrees(Double(index) * (360.0 / Double(dashCount))))
                    }
                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .opacity(appearOpacity)
            .scaleEffect(appoarScale)
        }
        .onAppear {
            withAnimation(.easeIn(duration: 1).delay(1)) {
                appearOpacity = 1
                appoarScale = 1
            }
            startAnimation()
        }
        .onDisappear {
            timer?.invalidate()
            timer = nil
        }
    }

    private func opacityFor(index: Int) -> Double {
        let distance = abs((index - currentIndex) % dashCount)
        let wrappedDistance = min(distance, dashCount - distance)

        let visibleDashes = 10
        if wrappedDistance <= visibleDashes {
            return 0.2 + Double(visibleDashes - wrappedDistance) * 0.2 // Max opacity 1.0 at current position
        }

        return 0.1 // Base opacity for non-highlighted dashes
    }

    private func startAnimation() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.03, repeats: true) { _ in
            withAnimation {
                currentIndex = (currentIndex + 1) % dashCount
            }
        }
    }
}

#Preview {
    LoadingView()
}
