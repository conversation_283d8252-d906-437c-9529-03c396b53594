//
//  ImagePlaceholderView.swift
//  Appio App
//
//  Created by gondo on 09/07/2023.
//

import SwiftUI

struct ImagePlaceholderView: View {
    let cornerRadius: CGFloat

    init(cornerRadius: CGFloat = 0) {
        self.cornerRadius = cornerRadius
    }

    var body: some View {
        GeometryReader { geometry in
            if cornerRadius > 0 {
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(Color.gray.opacity(0.3))
                    .overlay(placeholderIcon(for: geometry.size))
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(placeholderIcon(for: geometry.size))
            }
        }
    }

    private func placeholderIcon(for size: CGSize) -> some View {
        // Determine icon size based on the placeholder dimensions
        let iconFont: Font = determineIconFont(for: size)

        return Image(systemName: "photo")
            .symbolVariant(.fill)
            .foregroundStyle(.gray)
            .font(iconFont)
    }

    private func determineIconFont(for size: CGSize) -> Font {
        // Calculate the smaller dimension to ensure icon fits properly
        let smallerDimension = min(size.width, size.height)

        switch smallerDimension {
        case 0 ..< 30:
            return .caption
        case 30 ..< 50:
            return .body
        case 50 ..< 100:
            return .title3
        case 100 ..< 200:
            return .title
        default:
            return .largeTitle
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Rectangle placeholder (no corners)
        ImagePlaceholderView()
            .frame(width: 200, height: 112)

        // Rounded placeholder
        ImagePlaceholderView(cornerRadius: 15)
            .frame(width: 48, height: 48)

        // Extra small placeholder to test dynamic sizing
        ImagePlaceholderView(cornerRadius: 5)
            .frame(width: 25, height: 25)

        // Large placeholder to test dynamic sizing
        ImagePlaceholderView(cornerRadius: 20)
            .frame(width: 200, height: 200)
    }
    .padding()
}
