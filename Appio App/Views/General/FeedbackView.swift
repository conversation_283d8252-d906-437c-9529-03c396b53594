//
//  FeedbackView.swift
//  Appio
//
//  Created by gondo on 09/09/2025.
//

import SwiftUI

struct FeedbackView: View {
    let service: ServiceEntity?
    let device: DeviceEntity?
    
    let maxLength: Int = 5000
    let onDismiss: () -> Void
    
    @State private var feedbackText: String = ""
    @State private var submissionResult: Bool? = nil
    @FocusState private var isEditorFocused: Bool
    
    private var characterCount: Int { feedbackText.count }
    private var warningThreshold: Int { Int(Double(maxLength) * 0.9) }
    private var counterColor: Color { characterCount >= warningThreshold ? .red : .secondary }
    private var isSubmitDisabled: Bool {
        feedbackText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            if let result = submissionResult, result == true {
                
                Spacer()
                
                Text("Thank you for your feedback")
                    .font(.largeTitle)
                    .bold()
                    .padding(.top, UIConstants.largeSpacing)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .multilineTextAlignment(.center)
                
            } else {
                Text("Feedback")
                    .font(.largeTitle)
                    .bold()
                    .padding(.top, UIConstants.largeSpacing)
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $feedbackText)
                        .focused($isEditorFocused)
                        .frame(height: 100)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.secondary.opacity(0.3))
                        )
                        .onChange(of: feedbackText) { oldValue, newValue in
                            if newValue.count > maxLength {
                                feedbackText = String(newValue.prefix(maxLength))
                            }
                            
                            // Reset error message
                            submissionResult = nil
                        }
                        .onAppear {
                            isEditorFocused = true
                        }
                    
                    // Placeholder
                    if feedbackText.isEmpty && !isEditorFocused {
                        Text("Found a problem? Let us know.")
                            .foregroundColor(.secondary)
                            .padding(.vertical, UIConstants.spacing)
                            .padding(.horizontal, 14)
                            .allowsHitTesting(false)
                    }
                }
                
                // Character counter
                HStack {
                    if let result = submissionResult, result == false {
                        Text("Failed to send feedback. Please try again.")
                            .font(.footnote)
                            .foregroundStyle(.red)
                    }
                    
                    Spacer()
                    
                    Text("\(String(characterCount))/\(String(maxLength))")
                        .font(.footnote)
                        .monospacedDigit()
                        .foregroundColor(counterColor)
                }
                .padding(.bottom, UIConstants.spacing)
                
                // Submit button
                Button {
                    // Reset error message
                    submissionResult = nil
                    Task {
                        submissionResult = await FeedbackManager.submitFeedback(serviceId: service?.id ?? "", deviceId: "", message: feedbackText)
                        print("Feedback submit: \($submissionResult)")
                    }
                } label: {
                    Text("Submit")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.accentColor)
                        .foregroundStyle(.white)
                        .clipShape(Capsule())
                }
                .disabled(isSubmitDisabled)
                .opacity(isSubmitDisabled ? 0.5 : 1.0)
            }
            
            // Dismiss button
            HStack {
                Spacer()
                
                Button("Dismiss") {
                    onDismiss()
                }
                .padding(.top, UIConstants.smallSpacing)
                
                Spacer()
            }
            .padding(.top, UIConstants.spacing)
            
            Spacer()
            
            // Powered by button
            HStack(alignment: .center) {
                PoweredByView(service: service)
            }
            .frame(maxWidth: .infinity)
            .padding(.top, UIConstants.largeSpacing)
        }
        .padding()
    }
}

#Preview {
    #if DEBUG
    FeedbackView(service: ServiceEntity.mock, device: DeviceEntity.mock, onDismiss: {})
    #endif
}
