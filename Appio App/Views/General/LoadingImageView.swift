//
//  LoadingImageView.swift
//  Appio
//
//  Created by gondo on 14/03/2025.
//

import SwiftUI

struct LoadingViewView: View {
    @State private var appearOpacity: Double = 0

    let cornerRadius: CGFloat

    init(cornerRadius: CGFloat = 0) {
        self.cornerRadius = cornerRadius
    }

    var body: some View {
        ZStack {
            if cornerRadius > 0 {
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(Color.gray.opacity(0.3))
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }

            ImageLoaderShape(color: .primary)
                .opacity(appearOpacity)
                .onAppear {
                    withAnimation(.easeIn(duration: 0.1).delay(1)) {
                        appearOpacity = 1
                    }
                }
        }
    }
}

#Preview {
    LoadingViewView()
}
