//
//  DebugView.swift
//  Appio
//
//  Created by gondo on 18/03/2025.
//

import SwiftUI

struct DebugView: View {
    @EnvironmentObject var appViewModel: AppViewModel

    var body: some View {
        VStack(spacing: 8) {
            Text("Debug Console")
                .font(.title)
                .bold()
                .padding(.top, 50)

            Spacer()

            Text("Device:")
            Text(appViewModel.device?.id ?? "(no device id)")
                .onTapGesture {
                    UIPasteboard.general.string = appViewModel.device?.id
                }

            Spacer()

            Text("Services:")
            ForEach(appViewModel.services) { item in
                Text(item.id)
                    .onTapGesture {
                        UIPasteboard.general.string = item.id
                    }
            }

            Spacer()

            Text("Widgets:")
            ForEach(StorageManager.widgets.flatMap { $0.value }) { item in
                Text(item.serviceId.suffix(4) + " | " + item.id)
            }

            Spacer()

            Text("Notifications:")
            ForEach(StorageManager.notifications.flatMap { $0.value }) { item in
                Text(item.serviceId.suffix(4) + " | " + item.id)
            }

            Spacer()

            Text("Token:")
            Text(appViewModel.device?.apnToken ?? "(no apn token)")
                .onTapGesture {
                    UIPasteboard.general.string = appViewModel.device?.apnToken
                }

            Spacer()

            Button("Delete data") {
                StorageManager.device = nil
                StorageManager.services = []
                StorageManager.notifications = [:]
                StorageManager.widgets = [:]

                Task {
                    await appViewModel.start()
                }
            }
            .buttonStyle(.bordered)
            .tint(.red)

            Spacer()
        }
    }
}

#Preview {
    DebugView()
        .environmentObject(AppViewModel())
}
