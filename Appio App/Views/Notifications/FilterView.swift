//
//  FilterView.swift
//  Appio
//
//  Created by gondo on 25/03/2025.
//

import SwiftUI

struct FilterOptions: Equatable {
    var linksOnly: Bool = false
    var imagesOnly: Bool = false
    var dateRange: DateRange = .all

    enum DateRange: String, CaseIterable {
        case all = "All Time"
        case olderWeek = "Older Than Week"

        func isDateIncluded(_ date: Date) -> Bool {
            let calendar = Calendar.current
            let now = Date()

            switch self {
            case .all:
                return true
            case .olderWeek:
                return date <= calendar.date(byAdding: .day, value: -7, to: now)!
            }
        }
    }

    func isFiltered() -> Bool {
        return self != FilterOptions()
    }
}

struct FilterView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var filterOptions: FilterOptions

    var body: some View {
        NavigationStack {
            Form {
                Section {
                    Toggle("With Links Only", isOn: $filterOptions.linksOnly)
                    Toggle("With Images Only", isOn: $filterOptions.imagesOnly)
                }

                Section("Date Range") {
                    ForEach(FilterOptions.DateRange.allCases, id: \.self) { range in
                        HStack {
                            Text(range.rawValue)
                            Spacer()
                            if filterOptions.dateRange == range {
                                Image(systemName: "checkmark")
                                    .foregroundStyle(Color.accentColor)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            filterOptions.dateRange = range
                        }
                    }
                }
            }
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .cancellationAction) {
                    Button("Reset") {
                        filterOptions = FilterOptions()
                    }
                }
            }
        }
    }
}

#Preview {
    FilterView(filterOptions: .constant(FilterOptions()))
}
