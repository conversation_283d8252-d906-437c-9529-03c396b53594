//
//  NotificationDetails.swift
//  Appio
//
//  Created by gondo on 25/03/2025.
//

import SwiftUI

struct NotificationDetails: View {
    var notification: NotificationEntity

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                if let imageUrl = notification.payload.imageUrl, !imageUrl.isEmpty {
                    AsyncImageView(urlString: imageUrl)
                        .aspectRatio(16 / 9, contentMode: .fit)
                        .clipped()
                }

                Text(notification.receivedAt.humanReadable(separator: " - "))
                    .font(.footnote)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()

                Text(notification.payload.message)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
            }
        }
        .safeAreaInset(edge: .bottom) {
            if let link = notification.payload.link,
               let url = URL(string: link),
               UIApplication.shared.canOpenURL(url)
            {
                ZStack {
                    ZStack(alignment: .top) {
                        BackgroundColor()
                        VStack(spacing: 0) {
                            Divider()

                            Button {
                                UIApplication.shared.open(url)
                            } label: {
                                Text("OPEN LINK")
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, 24)
                                    .padding(.vertical, 12)
                                    .foregroundStyle(.white)
                                    .background(Color.accentColor)
                                    .clipShape(Capsule())
                            }
                            .padding(.top, UIConstants.largeSpacing)
                        }
                    }
                    .frame(height: 60)
                }
            }
        }
        .navigationTitle(notification.payload.subtitle)
    }
}

#if DEBUG
    #Preview {
        NotificationDetails(notification: .mock)
    }

    #Preview("image") {
        NotificationDetails(notification: .mockB)
    }
#endif
