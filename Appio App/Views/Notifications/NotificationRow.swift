//
//  NotificationRow.swift
//  Appio
//
//  Created by gondo on 18/03/2025.
//

import SwiftUI

struct NotificationRow: View {
    let notification: NotificationEntity

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(notification.payload.subtitle)
                        .lineLimit(1)
                        .font(.headline)

                    Spacer()

                    Text(notification.receivedAt.humanReadable())
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.trailing)
                }

                Text(notification.payload.message)
                    .lineLimit(3)
                    .font(.subheadline)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(UIColor.tertiarySystemGroupedBackground))
        .cornerRadius(UIConstants.smallCellCornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: UIConstants.bigCellCornerRadius)
                .strokeBorder(Color(UIColor.systemBackground).opacity(0.3), lineWidth: 1)
        )
    }
}

#if DEBUG
    #Preview {
        NotificationRow(notification: NotificationEntity.mock)
    }

    #Preview {
        NotificationRow(notification: NotificationEntity.mockB)
    }

    #Preview {
        NotificationRow(notification: NotificationEntity.mockC)
    }
#endif
