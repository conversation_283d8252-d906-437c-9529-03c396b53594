//
//  QRScannerCamera.swift
//  Appio App
//
//  Created by gondo on 27/10/2024.
//

import SwiftUI

struct QRScannerCamera: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.scenePhase) private var scenePhase

    @ObservedObject var viewModel: QRScannerModel
    @State private var isTorchOn: Bool = false

    @State private var originalBrightness: CGFloat = UIScreen.main.brightness
    @State private var hasBrightnessPermission = false
    @State private var brightnessTimer: Timer?

    var body: some View {
        ZStack {
            // MARK: - camera

            #if !targetEnvironment(simulator)
                QRScanner(viewModel: viewModel, isTorchOn: $isTorchOn)
                    .ignoresSafeArea()
            #endif

            // MARK: - pulsing square

            QRScannerOverlay()

            // MARK: - Top bar. Placed as last, to be clickable. - Using NavigationStack button now

            //
            //            ZStack(alignment: .top) {
            //                let topBarHeight = 50.0
            //
            //                VStack {
            //                    HStack {
            //                        Button {
            //                            restoreBrightness()
            //                            dismiss()
            //                        } label: {
            //                            Image(systemName: "chevron.left")
            //                                .foregroundStyle(.white)
            //                                .font(.system(size: 20, weight: .bold))
            //                                .padding()
            //                        }
            //
            //                        Spacer()
            //
            //                        // Title
            //                        Text("Find a QR code to scan")
            //                            .foregroundStyle(.white)
            //                            .font(.headline)
            //                            .bold()
            //
            //                        Spacer()
            //
            //                        Spacer()
            //                            .frame(width: 50)
            //
            //                    }
            //                    .frame(height: topBarHeight)
            //
            //                    Spacer()
            //                }
            //            }

            // MARK: - Torch Button at the bottom

            VStack {
                Spacer()
                Button{
                    isTorchOn.toggle()
                } label: {
                    if isTorchOn {
                        Image(systemName: "flashlight.on.fill")
                            .font(.title)
                            .padding(20)
                            .background(.white)
                            .foregroundStyle(Color.accentColor)
                            .clipShape(Circle())
                        //                            .shadow(radius: 10)
                    } else {
                        Image(systemName: "flashlight.off.fill")
                            .font(.title)
                            .padding(20)
                            .background(.black)
                            .foregroundStyle(.white)
                            .clipShape(Circle())
                        //                            .shadow(radius: 10)
                    }
                }
                .padding(.bottom, 40)
            }

            // MARK: - Validation label

            Button {
                tapScannedURL()
            } label: {
                HStack {
                    Image(systemName: "safari.fill")
                        .font(.system(size: 14, weight: .semibold))
                    Text(urlToShow)
                        .lineLimit(1)
                        .truncationMode(.tail)
                        .font(.callout)
                        .fontWeight(.medium)
                        .padding(.leading, -4)
                }
                .padding(.horizontal, 14)
                .padding(.vertical, 8)
                .foregroundStyle(.black)
                .background(Color(red: 1.0, green: 0.8, blue: 0.0))
                .clipShape(Capsule())
            }
            .buttonStyle(NoPressEffectButtonStyle())
            .padding(.top, 300)
            .frame(maxWidth: 300)
            .transition(.opacity)
            .animation(.easeInOut, value: urlToShow.isEmpty)
            .opacity(urlToShow.isEmpty ? 0 : 1)
        }
        .onChange(of: viewModel.publicURL) {
            handleUrlChange(viewModel.publicURL)
        }
        .onChange(of: scenePhase) {
            switch scenePhase {
            case .active:
                urlToShow = ""
                increaseBrightness()
            case .inactive, .background:
                restoreBrightness()
            @unknown default:
                restoreBrightness()
            }
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                increaseBrightness()
            }
        }
        .onDisappear {
            restoreBrightness()
        }
        //        .navigationBarBackButtonHidden(true) // too late
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    restoreBrightness()
                    dismiss()
                } label: {
                    HStack {
                        Image(systemName: "chevron.left")
                            .foregroundStyle(.white)
                            .bold()
                    }
                }
            }
            ToolbarItem(placement: .principal) {
                Text("Find a QR code to scan")
                    .foregroundStyle(.white)
                    .font(.headline)
                    .bold()
            }
        }
    }

    private func increaseBrightness() {
        brightnessTimer?.invalidate()

        // First check if we can modify brightness
        guard UIScreen.main.brightness < 1.0 else { return }

        // Store original only if we haven't already
        if originalBrightness == 0 {
            originalBrightness = UIScreen.main.brightness
        }

        brightnessIncrease(duration: 0.4)
    }

    private func restoreBrightness() {
        brightnessTimer?.invalidate()
        brightnessTimer = nil

        // Only restore if we actually changed it
        if originalBrightness > 0 {
            UIScreen.main.brightness = originalBrightness
            originalBrightness = 0
        }
    }

    private func brightnessIncrease(duration _: TimeInterval) {
        let step: CGFloat = 0.05 // Small step for gradual change
        let targetBrightness = 1.0

        // Calculated increase, not good
        // let totalSteps = Int(duration / step) // Number of steps (e.g., 20 steps for 1 second)
        // let currentBrightness = UIScreen.main.brightness
        // let brightnessIncrement = (targetBrightness - currentBrightness) / CGFloat(totalSteps)

        // Manual increase
        let brightnessIncrement = 0.1 // range: 0.05, 0.125

        brightnessTimer = Timer.scheduledTimer(withTimeInterval: step, repeats: true) { _ in
            if UIScreen.main.brightness < targetBrightness {
                UIScreen.main.brightness += brightnessIncrement
            } else {
                brightnessTimer?.invalidate()
            }
        }
    }

    @State private var urlToShow = ""
    @State private var hideLabelWorkItem: DispatchWorkItem? // Track the delayed hide task

    private func handleUrlChange(_ url: URL?) {
        // Cancel any pending hide task if the variable changes
        hideLabelWorkItem?.cancel()

        // Directly update urlToShow based on URL presence
        withAnimation {
            urlToShow = url != nil ? viewModel.scannedValue : ""
        }

        // Only set up hide timer if URL exists
        if url != nil {
            let workItem = DispatchWorkItem {
                withAnimation {
                    urlToShow = ""
                }
            }
            hideLabelWorkItem = workItem
            DispatchQueue.main.asyncAfter(deadline: .now() + 1, execute: workItem)
        }
    }

    private func tapScannedURL() {
        guard let url = URL(string: viewModel.scannedValue),
              url.scheme?.lowercased() == "https"
        else {
            return
        }

        restoreBrightness()
        UIApplication.shared.open(url)
    }
}

// Custom button style that removes the default press effect
struct NoPressEffectButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label // Return the button label without additional effects
    }
}

#Preview {
    QRScannerCamera(viewModel: QRScannerModel.mockURL)
}
