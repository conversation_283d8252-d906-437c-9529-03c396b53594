//
//  QRScanner.swift
//  Appio App
//
//  Created by gondo on 25/10/2024.
//

import SwiftUI

struct QRScanner: UIViewControllerRepresentable {
    @ObservedObject var viewModel: QRScannerModel
    @Binding var isTorchOn: Bool

    func makeCoordinator() -> Coordinator {
        return Coordinator(scannerView: self)
    }

    func makeUIViewController(context: Context) -> QRScannerController {
        QRScannerController(scannerDelegate: context.coordinator)
    }

    func updateUIViewController(_ uiViewController: QRScannerController, context _: Context) {
        uiViewController.toggleTorch(on: isTorchOn)
    }

    // Coordinator Layer
    final class Coordinator: NSObject, ScannerVCDelegate {
        private let scannerView: QRScanner

        init(scannerView: QRScanner) {
            self.scannerView = scannerView
        }

        func didFind(value: String) {
            print("Scanned value: \(value)")

            scannerView.viewModel.scannedValue = value
        }

        func didSurface(error: CameraError) {
            // Clear out
            scannerView.viewModel.scannedValue = ""

            print("Scan error: \(error)")
        }
    }
}

#Preview {
    QRScanner(viewModel: QRScannerModel.mockAppio, isTorchOn: .constant(false))
}
