//
//  QRScannerOverlay.swift
//  Appio App
//
//  Created by gondo on 25/10/2024.
//

import SwiftUI

struct QRScannerOverlay: View {
    @State private var scale: CGFloat = 1.0

    var body: some View {
        GeometryReader { _ in
            ZStack {
                CustomDrawViewRepresentable()
                    .scaleEffect(scale)
            }
            .onAppear {
                startBreathingAnimation()
            }
        }
        .edgesIgnoringSafeArea(.all)
    }

    private func startBreathingAnimation() {
        // Quick inhale
        withAnimation(.easeIn(duration: 0.4)) {
            scale = 1.1 // Scale up for quick inhale
        }

        // Quick exhale
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeOut(duration: 0.4)) {
                scale = 1.0
            }

            // Longer pause before the next breath
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                startBreathingAnimation() // Repeat the breathing cycle
            }
        }
    }
}

#Preview {
    QRScannerOverlay()
}

// Create a custom UIView class
class CustomDrawView: UIView {
    // MARK: - OverlayScannerPreviewLayer

    public var cornerLength: CGFloat = 40
    public var cornerRadius: CGFloat = 30

    public var lineWidth: CGFloat = 7
    public var lineFillColor: UIColor = .white
    public var lineBorderColor: UIColor = .black.withAlphaComponent(0.2)
    public var lineCap: CAShapeLayerLineCap = .round

    public var maskSize: CGSize = .init(width: 200, height: 200)

    private var maskContainer: CGRect {
        CGRect(x: (bounds.width / 2) - (maskSize.width / 2),
               y: (bounds.height / 2) - (maskSize.height / 2),
               width: maskSize.width, height: maskSize.height)
    }

    override func draw(_: CGRect) {
        let path = CGMutablePath()
        path.addRect(bounds)
        path.addRoundedRect(in: maskContainer, cornerWidth: cornerRadius, cornerHeight: cornerRadius)

        // MARK: - Semi-transparent background

        //        let maskLayer = CAShapeLayer()
        //        maskLayer.path = path
        //        maskLayer.fillColor = UIColor.black.withAlphaComponent(0.5).cgColor
        //        maskLayer.fillRule = .evenOdd
        //
        //        self.layer.addSublayer(maskLayer)

        // MARK: - Edged Corners

        if cornerRadius > cornerLength { cornerRadius = cornerLength }
        if cornerLength > maskContainer.width / 2 { cornerLength = maskContainer.width / 2 }

        let upperLeftPoint = CGPoint(x: maskContainer.minX, y: maskContainer.minY)
        let upperRightPoint = CGPoint(x: maskContainer.maxX, y: maskContainer.minY)
        let lowerRightPoint = CGPoint(x: maskContainer.maxX, y: maskContainer.maxY)
        let lowerLeftPoint = CGPoint(x: maskContainer.minX, y: maskContainer.maxY)

        let upperLeftCorner = UIBezierPath()
        upperLeftCorner.move(to: upperLeftPoint.offsetBy(dx: 0, dy: cornerLength))
        upperLeftCorner.addArc(withCenter: upperLeftPoint.offsetBy(dx: cornerRadius, dy: cornerRadius),
                               radius: cornerRadius, startAngle: .pi, endAngle: 3 * .pi / 2, clockwise: true)
        upperLeftCorner.addLine(to: upperLeftPoint.offsetBy(dx: cornerLength, dy: 0))

        let upperRightCorner = UIBezierPath()
        upperRightCorner.move(to: upperRightPoint.offsetBy(dx: -cornerLength, dy: 0))
        upperRightCorner.addArc(withCenter: upperRightPoint.offsetBy(dx: -cornerRadius, dy: cornerRadius),
                                radius: cornerRadius, startAngle: 3 * .pi / 2, endAngle: 0, clockwise: true)
        upperRightCorner.addLine(to: upperRightPoint.offsetBy(dx: 0, dy: cornerLength))

        let lowerRightCorner = UIBezierPath()
        lowerRightCorner.move(to: lowerRightPoint.offsetBy(dx: 0, dy: -cornerLength))
        lowerRightCorner.addArc(withCenter: lowerRightPoint.offsetBy(dx: -cornerRadius, dy: -cornerRadius),
                                radius: cornerRadius, startAngle: 0, endAngle: .pi / 2, clockwise: true)
        lowerRightCorner.addLine(to: lowerRightPoint.offsetBy(dx: -cornerLength, dy: 0))

        let bottomLeftCorner = UIBezierPath()
        bottomLeftCorner.move(to: lowerLeftPoint.offsetBy(dx: cornerLength, dy: 0))
        bottomLeftCorner.addArc(withCenter: lowerLeftPoint.offsetBy(dx: cornerRadius, dy: -cornerRadius),
                                radius: cornerRadius, startAngle: .pi / 2, endAngle: .pi, clockwise: true)
        bottomLeftCorner.addLine(to: lowerLeftPoint.offsetBy(dx: 0, dy: -cornerLength))

        let combinedPath = CGMutablePath()
        combinedPath.addPath(upperLeftCorner.cgPath)
        combinedPath.addPath(upperRightCorner.cgPath)
        combinedPath.addPath(lowerRightCorner.cgPath)
        combinedPath.addPath(bottomLeftCorner.cgPath)

        // MARK: - Path border

        let shapeLayerBorder = CAShapeLayer()
        shapeLayerBorder.path = combinedPath
        shapeLayerBorder.strokeColor = lineBorderColor.cgColor
        shapeLayerBorder.fillColor = UIColor.clear.cgColor
        shapeLayerBorder.lineWidth = lineWidth + 1
        shapeLayerBorder.lineCap = lineCap

        layer.addSublayer(shapeLayerBorder)

        // MARK: - Path fill

        let shapeLayer = CAShapeLayer()
        shapeLayer.path = combinedPath
        shapeLayer.strokeColor = lineFillColor.cgColor
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.lineWidth = lineWidth
        shapeLayer.lineCap = lineCap

        layer.addSublayer(shapeLayer)
    }
}

// Wrap the UIView inside SwiftUI using UIViewRepresentable
struct CustomDrawViewRepresentable: UIViewRepresentable {
    func makeUIView(context _: Context) -> CustomDrawView {
        return CustomDrawView()
    }

    func updateUIView(_: CustomDrawView, context _: Context) {
        // No update needed for static drawing
    }
}

extension CGPoint {
    func offsetBy(dx: CGFloat, dy: CGFloat) -> CGPoint {
        var point = self
        point.x += dx
        point.y += dy
        return point
    }
}
