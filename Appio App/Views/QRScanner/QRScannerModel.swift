//
//  QRScannerModel.swift
//  Appio App
//
//  Created by gondo on 27/10/2024.
//

import AVFoundation
import SwiftUI

enum CameraStatus {
    case allowed, denied, unknonw
}

class QRScannerModel: ObservableObject {
    @Published var appioURL: URL?
    @Published var publicURL: URL?
    @Published var cameraStatus: CameraStatus = .unknonw
    @Published var scannedValue: String = "" {
        didSet {
            validate(scannedValue)
        }
    }

    func validate(_ value: String) {
        // reset
        appioURL = nil
        publicURL = nil

        guard let url = URL(string: value) else {
            return
        }

        if let _ = parseParams(from: url) {
            appioURL = url
            return
        }

        if UIApplication.shared.canOpenURL(url) {
            publicURL = url
            return
        }
    }

    private func parseParams(from url: URL) -> URLparams? {
        do {
            return try URLparser.parseParams(from: url)
        } catch {
            return nil
        }
    }

    func checkCameraPermission() async {
        let status = await requestCameraPermission()
        await MainActor.run {
            cameraStatus = status
        }
    }

    private func requestCameraPermission() async -> CameraStatus {
        let status = AVCaptureDevice.authorizationStatus(for: .video)

        switch status {
        case .notDetermined:
            if await AVCaptureDevice.requestAccess(for: .video) {
                return .allowed
            } else {
                return .denied
            }
        case .restricted, .denied:
            return .denied
        case .authorized:
            return .allowed
        @unknown default:
            return .denied
        }
    }

    // MARK: - Mock instance for previews

    static var mockAppio: QRScannerModel {
        let viewModel = QRScannerModel()
        viewModel.scannedValue = "appio://appio/?service=demo_svc_01jndpe4fnn90wkpvw4d28hwd0&user=user1"
        viewModel.appioURL = URL(string: "appio://appio/?service=demo_svc_01jndpe4fnn90wkpvw4d28hwd0&user=user1")
        return viewModel
    }

    static var mockURL: QRScannerModel {
        let viewModel = QRScannerModel()
        viewModel.scannedValue = "https://appio.so"
        viewModel.publicURL = URL(string: "https://appio.so")
        return viewModel
    }
}
