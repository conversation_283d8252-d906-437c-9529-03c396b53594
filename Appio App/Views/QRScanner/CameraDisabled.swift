//
//  CameraDisabled.swift
//  Appio App
//
//  Created by gondo on 27/10/2024.
//

import SwiftUI

struct CameraDisabled: View {
    @Environment(\.dismiss) var dismiss

    var body: some View {
//        ZStack {
//            // Back button - Using NavigationStack button now
//            VStack {
//                HStack {
//                    Button {
//                        dismiss()
//                    } label: {
//                        HStack(spacing: 4) {
//                            Image(systemName: "chevron.left")
//                                .font(.system(size: 20, weight: .semibold))
//                                .foregroundStyle(Color.accentColor)
//                            Text("Back")
//                                .font(.system(size: 17))
//                                .foregroundStyle(Color.accentColor)
//                        }
//                        .padding(.leading, 20)
//                        .padding(.top, 12)
//                    }
//
//                    Spacer()
//                }
//
//                Spacer()
//            }

        // Instruct user to enable camera permissions
        VStack(spacing: 20) {
            Text("Camera access is required")
                .multilineTextAlignment(.center)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundStyle(.primary)

            Text("Please enable camera access in Settings.")
                .font(.body)
                .foregroundStyle(.primary)
                .padding(.horizontal, 40)
                .multilineTextAlignment(.center)

            Button("Open Settings") {
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(url)
                }
            }
            .padding(.top, 10)
            .font(.title3)
        }
        .padding()
//        }
    }
}

#Preview {
    CameraDisabled()
}
