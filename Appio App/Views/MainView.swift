//
//  MainView.swift
//  Appio App
//
//  Created by gondo on 22/10/2024.
//

import SwiftUI

struct MainView: View {
    #if DEBUG
        @State private var showDebug = false
    #endif

    @EnvironmentObject var appViewModel: AppViewModel

    var body: some View {
        ZStack {
            BackgroundColor()
            MainContentView()
            LaunchFadeLogoScreen()
        }
        .accessibilityLabel("Main view")
        .task {
            NotificationDelegate.shared.setAppViewModel(appViewModel) // so we can change screen based on received notification
        }
        .overlay {
            if appViewModel.isLoading {
                LoadingOverlay()
            }
        }
        #if DEBUG
        .sheet(isPresented: $showDebug) {
                DebugView()
                    .environmentObject(appViewModel)
            }
            .onShake {
                showDebug.toggle()
            }
        #endif
    }
}

private struct MainContentView: View {
    @EnvironmentObject private var appViewModel: AppViewModel

    private let contentZIndex: Double = 1

    var body: some View {
        ZStack {
            switch appViewModel.state {
            case .launch:
                EmptyView()
                    .id("launch")
                    .accessibilityLabel("Launch screen")
                    .transition(AnyTransition.opacity.combined(with: .scale))
                    .zIndex(contentZIndex)
            case .intro:
                IntroScreen()
                    .id("intro")
                    .accessibilityLabel("Introduction screen")
                    .transition(.opacity)
                    .zIndex(contentZIndex)
            case .service:
                ServicesFlowView()
                    .zIndex(contentZIndex)
            case let .error(error):
                ErrorScreen(error: error)
                    .id("error")
                    .accessibilityLabel("Error screen")
                    .transition(.scale.combined(with: .opacity))
                    .zIndex(contentZIndex)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appViewModel.state)
    }
}

#Preview {
    MainView()
        .environmentObject(AppViewModel())
}
