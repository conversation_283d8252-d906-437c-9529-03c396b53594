//
//  RequestPushNotificationPermissions.swift
//  Appio
//
//  Created by gondo on 12/03/2025.
//

import SwiftUI

struct RequestPushNotificationPermissions: View {
    @Environment(\.scenePhase) private var scenePhase

    @Binding var isOpen: Bool

    @State private var showNotDeterminedAlert = false
    @State private var showDeniedAlert = false
    @State private var isFreshlyDenied = false
//    @State private var arrowAnimationProgress = 0.0
//    @State private var isAnimatingArrow = false

//    private let arrowAnimationDuration: TimeInterval = 0.5
//    private let arrowAnimationPause: TimeInterval = 0.5

    /// init value
    let serviceTitle: String

    var body: some View {
        ZStack {
            if isOpen {
                BackgroundColor() // hide content below to remove distractions

                ArrowAnimation()
                    .frame(height: 30)
                    .position(x: UIScreen.main.bounds.width / 2 + 65, y: UIScreen.main.bounds.height / 2 + 150)

//                ZStack {
//                    Image("ArrowIndicator")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: 50)
//                        .scaleEffect(0.5 + (0.5 * arrowAnimationProgress), anchor: .bottom)
//                        .offset(y: -10 * arrowAnimationProgress)
//                        .opacity(0.0 + 0.5 * arrowAnimationProgress)
//                }
//                .position(x: UIScreen.main.bounds.width / 2 + 65, y: UIScreen.main.bounds.height / 2 + 140)
//                .onAppear {
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//                        startArrowAnimation()
//                    }
//                }
//                .onDisappear {
//                    isAnimatingArrow = false
//                }
            }
        }
        .ignoresSafeArea()
        .alert("\"\(serviceTitle.truncatedHumanReadable(to: 18))\" Would Like to Send You Notifications", isPresented: $showNotDeterminedAlert) {
            Button("Don't Allow") {
                isOpen = false
            }
            .disabled(true)

            Button("Allow", role: .none) {
                PushNotificationManager.requestPermission { granted in
                    isFreshlyDenied = !granted
                    isOpen = false
                }
            }
        } message: {
            Text("Notifications may include alerts, sounds and icon badges. These can be configured in Settings.")
        }
        .alert("\"\(serviceTitle)\" Would Like to Send You Notifications", isPresented: $showDeniedAlert) {
            Button("Dismiss") {
                isOpen = false
            }

            Button("Allow", role: .none) {
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    isOpen = false
                    UIApplication.shared.open(url)
                }
            }
        } message: {
            Text("Notifications may include alerts, sounds and icon badges. These can be configured in Settings.")
        }
        .onAppear {
            checkNotificationStatus()
        }
        .onChange(of: scenePhase) {
            if !isFreshlyDenied && scenePhase == .active {
                checkNotificationStatus()
            }
        }
    }

//    private func startArrowAnimation() {
//        isAnimatingArrow = true
//        arrowAnimationProgress = 0
//        arrowAnimateCycle()
//    }
//
//    private func arrowAnimateCycle() {
//        guard isAnimatingArrow else { return }
//
//        arrowAnimationProgress = 0
//
//        withAnimation(.easeOut(duration: arrowAnimationDuration)) {
//            arrowAnimationProgress = 1
//        }
//
//        DispatchQueue.main.asyncAfter(deadline: .now() + arrowAnimationDuration + arrowAnimationPause) {
//            if isAnimatingArrow {
//                arrowAnimateCycle()
//            }
//        }
//    }

    private func checkNotificationStatus() {
        print("Checking push notification permissions")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            Task {
                let authorizationStatus = await PushNotificationManager.authorizationStatus()

                print("Push notification permissions status (0=unknown, 1=false, 2=true): \(authorizationStatus)")

                if authorizationStatus == .notDetermined {
                    showNotDeterminedAlert = true
                    isOpen = true
                } else if authorizationStatus == .denied {
                    showDeniedAlert = true
                    isOpen = true
                } else {
                    isOpen = false

                    // Refresh token
                    await PushNotificationManager.refreshNotificationToken()
                }

                // Update Device settings
                if var device = DeviceManager.getCurrentDevice() {
                    let isEnabled = (authorizationStatus == .authorized)
                    if device.notifiationsEnabled != isEnabled {
                        device.notifiationsEnabled = isEnabled
                        StorageManager.device = device // this triggeres API update via DeviceSyncManager
                    }
                }
            }
        }
    }
}

#Preview {
    RequestPushNotificationPermissions(isOpen: .constant(true), serviceTitle: "Demo service with a very long name that should be shortened")
}
