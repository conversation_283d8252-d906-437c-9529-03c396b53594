//
//  APIFingerprint.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation

struct FingerprintRequest: Codable {
    let userAgent: String
    let screenResolution: String
    let language: String
    let timeOffset: Int

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case userAgent = "user_agent"
        case screenResolution = "screen_resolution"
        case language
        case timeOffset = "time_offset"
    }
}

struct FingerprintResponse: Codable {
    let fingerprintId: String
    let serviceId: String
    let customerUserId: String

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case fingerprintId = "fingerprint_id"
        case serviceId = "service_id"
        case customerUserId = "customer_user_id"
    }
}
