//
//  APIWidget.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import Foundation

struct WidgetResponse: Codable {
    let id: String
    let serviceId: String
    let name: String
    let config: String

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id
        case serviceId = "service_id"
        case name
        case config
    }
}
