//
//  APIFeatureFlag.swift
//  Appio
//
//  Created by gondo on 23/04/2025.
//

import Foundation

struct FeatureFlagResponse: Decodable {
    let id: String
    let version: String
    let config: AnyDecodable

    private enum CodingKeys: String, CodingKey {
        case id, version, config
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        version = try container.decode(String.self, forKey: .version)

        let configString = try container.decode(String.self, forKey: .config)
        guard let configData = configString.data(using: .utf8) else {
            throw DecodingError.dataCorruptedError(forKey: .config, in: container, debugDescription: "Invalid UTF-8 in config")
        }

        config = try JSONDecoder().decode(AnyDecodable.self, from: configData)
    }
}
