//
//  AppioShape.swift
//  Appio
//
//  Created by gondo on 08/03/2025.
//
//  Converted via desktop app SVGShaper.app
//

import SwiftUI

struct AppioShape: View {
    private static let intrinsicSize = CGSize(width: 880, height: 880)
    private static let viewBox = CGRect(x: -50.0, y: -50.0, width: 880, height: 880)

    private var color: Color
    private var strokeWidth: CGFloat

    init(color: Color, strokeWidth: CGFloat = 10) {
        self.color = color
        self.strokeWidth = strokeWidth
    }

    struct PathView1: View { // SVGPath
        var color: Color
        var strokeWidth: CGFloat

        init(color: Color, strokeWidth: CGFloat) {
            self.color = color
            self.strokeWidth = strokeWidth
        }

        struct PathShape1: Shape {
            // PathShape1 remains the same
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 415.706, y: 100.002))
                    path.addCurve(to: CGPoint(x: 402, y: 100),
                                  control1: CGPoint(x: 411.248, y: 100),
                                  control2: CGPoint(x: 406.681, y: 100))
                    path.addLine(to: CGPoint(x: 288, y: 100))
                    path.addCurve(to: CGPoint(x: 170.591, y: 102.419),
                                  control1: CGPoint(x: 237.43, y: 100),
                                  control2: CGPoint(x: 200.104, y: 100.008))
                    path.addCurve(to: CGPoint(x: 102.822, y: 118.529),
                                  control1: CGPoint(x: 141.194, y: 104.821),
                                  control2: CGPoint(x: 120.429, y: 109.557))
                    path.addCurve(to: CGPoint(x: 28.5289, y: 192.822),
                                  control1: CGPoint(x: 70.8341, y: 134.827),
                                  control2: CGPoint(x: 44.8274, y: 160.834))
                    path.addCurve(to: CGPoint(x: 12.4191, y: 260.591),
                                  control1: CGPoint(x: 19.5575, y: 210.429),
                                  control2: CGPoint(x: 14.821, y: 231.194))
                    path.addCurve(to: CGPoint(x: 10, y: 378),
                                  control1: CGPoint(x: 10.0078, y: 290.104),
                                  control2: CGPoint(x: 10, y: 327.43))
                    path.addLine(to: CGPoint(x: 10, y: 492))
                    path.addCurve(to: CGPoint(x: 12.4191, y: 609.409),
                                  control1: CGPoint(x: 10, y: 542.57),
                                  control2: CGPoint(x: 10.0078, y: 579.896))
                    path.addCurve(to: CGPoint(x: 28.5289, y: 677.178),
                                  control1: CGPoint(x: 14.821, y: 638.806),
                                  control2: CGPoint(x: 19.5575, y: 659.571))
                    path.addCurve(to: CGPoint(x: 102.822, y: 751.471),
                                  control1: CGPoint(x: 44.8274, y: 709.166),
                                  control2: CGPoint(x: 70.8341, y: 735.173))
                    path.addCurve(to: CGPoint(x: 170.591, y: 767.581),
                                  control1: CGPoint(x: 120.429, y: 760.443),
                                  control2: CGPoint(x: 141.194, y: 765.179))
                    path.addCurve(to: CGPoint(x: 288, y: 770),
                                  control1: CGPoint(x: 200.104, y: 769.992),
                                  control2: CGPoint(x: 237.43, y: 770))
                    path.addLine(to: CGPoint(x: 402, y: 770))
                    path.addCurve(to: CGPoint(x: 519.409, y: 767.581),
                                  control1: CGPoint(x: 452.57, y: 770),
                                  control2: CGPoint(x: 489.896, y: 769.992))
                    path.addCurve(to: CGPoint(x: 587.178, y: 751.471),
                                  control1: CGPoint(x: 548.806, y: 765.179),
                                  control2: CGPoint(x: 569.571, y: 760.443))
                    path.addCurve(to: CGPoint(x: 661.471, y: 677.178),
                                  control1: CGPoint(x: 619.166, y: 735.173),
                                  control2: CGPoint(x: 645.173, y: 709.166))
                    path.addCurve(to: CGPoint(x: 677.581, y: 609.409),
                                  control1: CGPoint(x: 670.443, y: 659.571),
                                  control2: CGPoint(x: 675.179, y: 638.806))
                    path.addCurve(to: CGPoint(x: 680, y: 492),
                                  control1: CGPoint(x: 679.992, y: 579.896),
                                  control2: CGPoint(x: 680, y: 542.57))
                    path.addLine(to: CGPoint(x: 680, y: 378))
                    path.addCurve(to: CGPoint(x: 679.998, y: 364.294),
                                  control1: CGPoint(x: 680, y: 373.319),
                                  control2: CGPoint(x: 680, y: 368.752))
                }
            }
        }

        var body: some View {
            ZStack {
                PathShape1()
                    .stroke(color, lineWidth: strokeWidth)
            }
        }
    }

    struct Circle1: View {
        var color: Color
        var strokeWidth: CGFloat

        init(color: Color, strokeWidth: CGFloat) {
            self.color = color
            self.strokeWidth = strokeWidth
        }

        static let intrinsicSize = CGSize(width: 290, height: 290)
        static let origin = CGPoint(x: 485, y: 5)

        var body: some View {
            Circle()
                .stroke(color, lineWidth: strokeWidth)
        }
    }

    struct Reactangle1: View {
        var color: Color
        var strokeWidth: CGFloat

        init(color: Color, strokeWidth: CGFloat) {
            self.color = color
            self.strokeWidth = strokeWidth
        }

        static let intrinsicSize = CGSize(width: 190, height: 145)
        static let origin = CGPoint(x: 145, y: 235)

        var body: some View {
            RoundedRectangle(cornerRadius: 55)
                .stroke(color, lineWidth: strokeWidth)
        }
    }

    struct Reactangle2: View {
        var color: Color
        var strokeWidth: CGFloat

        init(color: Color, strokeWidth: CGFloat) {
            self.color = color
            self.strokeWidth = strokeWidth
        }

        static let intrinsicSize = CGSize(width: 400, height: 145)
        static let origin = CGPoint(x: 145, y: 490)

        var body: some View {
            RoundedRectangle(cornerRadius: 55)
                .stroke(color, lineWidth: strokeWidth)
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                PathView1(color: color, strokeWidth: strokeWidth)
                Circle1(color: color, strokeWidth: strokeWidth)
                    .frame(width: 290, height: 290)
                    .position(x: 630, y: 150)
                Reactangle1(color: color, strokeWidth: strokeWidth)
                    .frame(width: 190, height: 145)
                    .position(x: 240, y: 307.5)
                Reactangle2(color: color, strokeWidth: strokeWidth)
                    .frame(width: 400, height: 145)
                    .position(x: 345, y: 562.5)
            }
            .frame(width: Self.intrinsicSize.width, height: Self.intrinsicSize.height)
            .scaleEffect(x: geometry.size.width / Self.intrinsicSize.width, y: geometry.size.height / Self.intrinsicSize.height)
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
    }
}

#Preview {
    VStack {
        AppioShape(color: .accentColor, strokeWidth: 20)
            .frame(width: 100, height: 100)
    }
}
